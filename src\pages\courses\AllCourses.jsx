import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>ilter, FaStar, FaRegClock, FaUserGraduate, FaChevronDown } from 'react-icons/fa';

const AllCourses = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  // Sample course data
  const courses = [
    {
      id: 1,
      title: "Complete Web Development Bootcamp",
      description: "Learn HTML, CSS, JavaScript, React, Node.js, MongoDB and more to become a full-stack web developer.",
      instructor: "Dr. <PERSON>",
      rating: 4.8,
      reviews: 1245,
      students: 12500,
      duration: "48 hours",
      level: "Beginner to Advanced",
      category: "development",
      price: "₹12,999",
      originalPrice: "₹19,999",
      image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 2,
      title: "Data Science and Machine Learning Masterclass",
      description: "Master data analysis, visualization, machine learning algorithms and deep learning with Python.",
      instructor: "Prof. Michael Chen",
      rating: 4.9,
      reviews: 987,
      students: 8900,
      duration: "56 hours",
      level: "Intermediate",
      category: "data-science",
      price: "₹14,999",
      originalPrice: "₹21,999",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 3,
      title: "UX/UI Design Fundamentals",
      description: "Learn user experience design principles, wireframing, prototyping and design thinking methodologies.",
      instructor: "Emma Rodriguez",
      rating: 4.7,
      reviews: 756,
      students: 6200,
      duration: "32 hours",
      level: "Beginner",
      category: "design",
      price: "₹9,999",
      originalPrice: "₹15,999",
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 4,
      title: "Advanced React and Redux",
      description: "Take your React skills to the next level with advanced patterns, hooks, context API and Redux.",
      instructor: "David Wilson",
      rating: 4.8,
      reviews: 632,
      students: 5400,
      duration: "28 hours",
      level: "Advanced",
      category: "development",
      price: "₹11,999",
      originalPrice: "₹17,999",
      image: "https://images.unsplash.com/photo-1633356122102-3fe601e05bd2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 5,
      title: "Financial Analysis and Modeling",
      description: "Learn financial statement analysis, valuation, forecasting, and financial modeling in Excel.",
      instructor: "Robert Chang",
      rating: 4.6,
      reviews: 521,
      students: 4800,
      duration: "36 hours",
      level: "Intermediate",
      category: "business",
      price: "₹10,999",
      originalPrice: "₹16,999",
      image: "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 6,
      title: "Digital Marketing Mastery",
      description: "Comprehensive guide to SEO, SEM, social media marketing, content marketing and analytics.",
      instructor: "Jennifer Lee",
      rating: 4.7,
      reviews: 689,
      students: 7300,
      duration: "42 hours",
      level: "Beginner to Intermediate",
      category: "marketing",
      price: "₹11,499",
      originalPrice: "₹16,999",
      image: "https://images.unsplash.com/photo-1432888622747-4eb9a8f5a07d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 7,
      title: "iOS App Development with Swift",
      description: "Build iOS applications from scratch using Swift and Xcode. Create real-world apps for the App Store.",
      instructor: "Alex Thompson",
      rating: 4.8,
      reviews: 542,
      students: 4900,
      duration: "38 hours",
      level: "Intermediate",
      category: "development",
      price: "₹13,499",
      originalPrice: "₹19,999",
      image: "https://images.unsplash.com/photo-1621839673705-6617adf9e890?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 8,
      title: "Artificial Intelligence Fundamentals",
      description: "Introduction to AI concepts, neural networks, natural language processing and computer vision.",
      instructor: "Dr. James Miller",
      rating: 4.9,
      reviews: 478,
      students: 3800,
      duration: "44 hours",
      level: "Intermediate to Advanced",
      category: "data-science",
      price: "₹15,999",
      originalPrice: "₹22,999",
      image: "https://images.unsplash.com/photo-1677442135136-760c813a743d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 9,
      title: "Project Management Professional (PMP) Certification",
      description: "Complete preparation for the PMP exam with practice tests and real-world case studies.",
      instructor: "Lisa Anderson",
      rating: 4.7,
      reviews: 612,
      students: 5600,
      duration: "35 hours",
      level: "Intermediate",
      category: "business",
      price: "₹12,499",
      originalPrice: "₹18,999",
      image: "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 10,
      title: "Graphic Design Masterclass",
      description: "Learn Adobe Photoshop, Illustrator, InDesign and principles of graphic design from scratch.",
      instructor: "Carlos Mendez",
      rating: 4.6,
      reviews: 589,
      students: 6100,
      duration: "40 hours",
      level: "Beginner",
      category: "design",
      price: "₹10,499",
      originalPrice: "₹15,999",
      image: "https://images.unsplash.com/photo-1626785774573-4b799315345d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 11,
      title: "Cybersecurity Fundamentals",
      description: "Learn network security, ethical hacking, cryptography, and security best practices.",
      instructor: "Mark Williams",
      rating: 4.8,
      reviews: 498,
      students: 4200,
      duration: "46 hours",
      level: "Beginner to Intermediate",
      category: "it",
      price: "₹13,999",
      originalPrice: "₹20,999",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: 12,
      title: "Cloud Computing with AWS",
      description: "Master Amazon Web Services including EC2, S3, Lambda, and more for cloud infrastructure.",
      instructor: "Sophia Garcia",
      rating: 4.7,
      reviews: 532,
      students: 4700,
      duration: "38 hours",
      level: "Intermediate",
      category: "it",
      price: "₹12,999",
      originalPrice: "₹18,999",
      image: "https://images.unsplash.com/photo-1614064641938-3bbee52942c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
    }
  ];

  // Categories for filter
  const categories = [
    { id: 'all', name: 'All Courses' },
    { id: 'development', name: 'Development' },
    { id: 'data-science', name: 'Data Science' },
    { id: 'design', name: 'Design' },
    { id: 'business', name: 'Business' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'it', name: 'IT & Software' }
  ];

  // Filter courses based on active category and search query
  const filteredCourses = courses.filter(course => {
    const matchesCategory = activeCategory === 'all' || course.category === activeCategory;
    const matchesSearch = course.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          course.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-teal-600 to-teal-700 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Explore Our Course Catalog
            </h1>
            <p className="text-teal-100 text-lg max-w-3xl mx-auto mb-8">
              Discover a wide range of courses designed to help you advance your career, learn new skills, and achieve your goals.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <input
                type="text"
                placeholder="Search for courses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-5 py-4 pr-12 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-teal-500 text-gray-700"
              />
              <FaSearch className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Category Tabs */}
        <div className="mb-8 overflow-x-auto pb-2">
          <div className="flex space-x-2 min-w-max">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium whitespace-nowrap transition-colors ${
                  activeCategory === category.id
                    ? 'bg-teal-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Filter Button (Mobile) */}
        <div className="md:hidden mb-6">
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-200 rounded-md shadow-sm"
          >
            <div className="flex items-center">
              <FaFilter className="text-gray-500 mr-2" />
              <span className="font-medium text-gray-700">Filters</span>
            </div>
            <FaChevronDown className={`text-gray-500 transition-transform ${showFilters ? 'transform rotate-180' : ''}`} />
          </button>
          
          {/* Mobile Filters */}
          {showFilters && (
            <div className="mt-2 p-4 bg-white border border-gray-200 rounded-md shadow-sm">
              {/* Filter options would go here */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Price</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Free</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Paid</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Level</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Beginner</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Intermediate</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Advanced</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Duration</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">0-10 hours</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">10-20 hours</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">20+ hours</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex flex-col md:flex-row">
          {/* Sidebar Filters (Desktop) */}
          <div className="hidden md:block w-64 flex-shrink-0 mr-8">
            <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm sticky top-24">
              <h2 className="font-bold text-gray-900 mb-4">Filters</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Price</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Free</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Paid</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Level</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Beginner</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Intermediate</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">Advanced</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Duration</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">0-10 hours</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">10-20 hours</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700">20+ hours</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Rating</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700 flex items-center">
                        4.5 & up <FaStar className="text-yellow-400 ml-1" size={12} />
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700 flex items-center">
                        4.0 & up <FaStar className="text-yellow-400 ml-1" size={12} />
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded text-teal-600 focus:ring-teal-500" />
                      <span className="ml-2 text-gray-700 flex items-center">
                        3.5 & up <FaStar className="text-yellow-400 ml-1" size={12} />
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Course Grid */}
          <div className="flex-1">
            <div className="mb-6 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-900">
                {filteredCourses.length} {filteredCourses.length === 1 ? 'course' : 'courses'} available
              </h2>
              <div className="hidden md:block">
                <select className="border border-gray-300 rounded-md py-2 px-4 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-teal-500">
                  <option>Most Popular</option>
                  <option>Highest Rated</option>
                  <option>Newest</option>
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                </select>
              </div>
            </div>
            
            {filteredCourses.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCourses.map(course => (
                  <div key={course.id} className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow border border-gray-200">
                    <div className="relative pb-[56.25%]">
                      <img 
                        src={course.image} 
                        alt={course.title}
                        className="absolute h-full w-full object-cover"
                      />
                    </div>
                    <div className="p-5">
                      <h3 className="font-bold text-gray-900 text-lg mb-2 line-clamp-2">{course.title}</h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{course.description}</p>
                      
                      <div className="flex items-center mb-3">
                        <p className="text-sm text-gray-700">By <span className="font-medium">{course.instructor}</span></p>
                      </div>
                      
                      <div className="flex items-center mb-3">
                        <div className="flex items-center">
                          <span className="text-yellow-400 font-bold mr-1">{course.rating}</span>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <FaStar 
                                key={i} 
                                className={`w-3 h-3 ${i < Math.floor(course.rating) ? 'text-yellow-400' : 'text-gray-300'}`} 
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-xs text-gray-500 ml-2">({course.reviews})</span>
                        <span className="text-xs text-gray-500 ml-4">{course.students.toLocaleString()} students</span>
                      </div>
                      
                      <div className="flex items-center text-xs text-gray-500 mb-4">
                        <div className="flex items-center mr-3">
                          <FaRegClock className="mr-1" />
                          <span>{course.duration}</span>
                        </div>
                        <div className="flex items-center">
                          <FaUserGraduate className="mr-1" />
                          <span>{course.level}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-bold text-gray-900">{course.price}</span>
                          <span className="text-gray-500 text-sm line-through ml-2">{course.originalPrice}</span>
                        </div>
                        <button className="bg-teal-600 text-white px-4 py-2 rounded text-sm hover:bg-teal-700 transition-colors">
                          View Course
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white p-8 rounded-lg text-center">
                <h3 className="text-xl font-medium text-gray-900 mb-2">No courses found</h3>
                <p className="text-gray-600">Try adjusting your search or filter criteria</p>
              </div>
            )}
            
            {/* Pagination */}
            {filteredCourses.length > 0 && (
              <div className="mt-10 flex justify-center">
                <nav className="flex items-center">
                  <button className="px-3 py-1 rounded-l-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50">
                    Previous
                  </button>
                  <button className="px-3 py-1 border-t border-b border-gray-300 bg-teal-600 text-white">
                    1
                  </button>
                  <button className="px-3 py-1 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    2
                  </button>
                  <button className="px-3 py-1 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    3
                  </button>
                  <button className="px-3 py-1 rounded-r-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    Next
                  </button>
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllCourses;