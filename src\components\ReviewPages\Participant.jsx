import React from 'react'

const Participant = () => {
    const reviews = [
        {
            id: 1,
            name: "<PERSON>",
            rating: "4.9/5",
            course: "<PERSON> Medical Coding Certification Course Review",
            review: "I am a nurse and was looking for a good medical coding course online as I want to move into a different career. Yes, working in shifts, I was afraid to take up a course in medical coding fearing I cannot cope with the timings. But <PERSON>'s Medical coding course came as a boon [...]",
            avatar: "👤",
            category: "Medical Coding Course"
        },
        {
            id: 2,
            name: "<PERSON><PERSON>",
            rating: "4.8/5",
            course: "<PERSON> Medical Coding Course Reviews",
            review: "The online classes provided by <PERSON> were interactive which helped me in a lot of things. First, it helped me to understand all the basic concepts, thanks to the experienced trainers in the field who made the concepts easy to learn. Second, I can ask and get cleared all of my doubts then [...]",
            avatar: "👤",
            category: "Medical Coding Course"
        },
        {
            id: 3,
            name: "<PERSON><PERSON><PERSON>arg",
            rating: "4.7/5",
            course: "<PERSON> Medical Coding Course Review",
            review: "I have always wanted to do a Medical coding course. But I was worried about the timing whether I can manage the course on top flexible for me and whether I can cope easily with the course. However, after checking <PERSON>'s medical coding course online I was confident and decided it is the best for me [...]",
            avatar: "👤",
            category: "Medical Coding Course"
        },
        {
            id: 4,
            name: "Simran Sha",
            rating: "4.6/5",
            course: "Henry Harvin Medical Coding Course Training Review",
            review: "Henry Harvin medical coding course helped me greatly to learn and master all the concepts such as HCPCS, ICD-10, and CPT. By taking this course, I was able to understand the concepts in a better way. The trainers were very supportive and helped me throughout the course [...]",
            avatar: "👤",
            category: "Medical Coding Course"
        },
        {
            id: 5,
            name: "Rahul Dutt",
            rating: "4.9/5",
            course: "Henry Harvin Medical Coding Course Review",
            review: "Opting for this online Medical coding course from Henry Harvin is the best decision I have ever made. One of my friends recommended this course to me and I was sceptical at first. But with the demo class, I decided that this can be the right course for me. My trainer [...]",
            avatar: "👤",
            category: "Medical Coding Course"
        },
        {
            id: 6,
            name: "Shilpa",
            rating: "4.8/5",
            course: "Henry Harvin ACCA Course Review",
            review: "ACCA is globally recognised and one of the most sought after credentials by employers worldwide. I study ACCA at Henry Harvin Institute. My experience with Henry Harvin is excellent. The faculty is very knowledgeable and experienced. The study material provided [...]",
            avatar: "👤",
            category: "Medical Coding Course"
        }
    ];

    return (
        <div style={{
            backgroundColor: '#f5f5f5',
            minHeight: '100vh',
            padding: '20px',
            fontFamily: 'Arial, sans-serif'
        }}>
            <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '20px',
                maxWidth: '1200px',
                margin: '0 auto'
            }}>
                {reviews.map((review) => (
                    <div key={review.id} style={{
                        backgroundColor: 'white',
                        borderRadius: '8px',
                        padding: '20px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        border: '1px solid #e0e0e0'
                    }}>
                        {/* Header with name and rating */}
                        <div style={{ marginBottom: '15px' }}>
                            <h3 style={{
                                color: '#7B2CBF',
                                fontSize: '16px',
                                fontWeight: 'bold',
                                margin: '0 0 5px 0'
                            }}>
                                {review.name}, {review.rating} {review.course.includes('Henry Harvin') ? review.course.split('Henry Harvin')[1] : review.course}
                            </h3>
                        </div>

                        {/* Course title */}
                        <h4 style={{
                            color: '#7B2CBF',
                            fontSize: '14px',
                            fontWeight: 'bold',
                            margin: '0 0 15px 0',
                            lineHeight: '1.4'
                        }}>
                            {review.course}
                        </h4>

                        {/* Review text */}
                        <p style={{
                            color: '#333',
                            fontSize: '13px',
                            lineHeight: '1.5',
                            margin: '0 0 20px 0',
                            textAlign: 'justify'
                        }}>
                            {review.review}
                        </p>

                        {/* Footer with avatar and category */}
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingTop: '15px',
                            borderTop: '1px solid #f0f0f0'
                        }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px'
                            }}>
                                <div style={{
                                    width: '32px',
                                    height: '32px',
                                    borderRadius: '50%',
                                    backgroundColor: '#e0e0e0',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '16px'
                                }}>
                                    {review.avatar}
                                </div>
                                <span style={{
                                    color: '#7B2CBF',
                                    fontSize: '12px',
                                    fontWeight: '500'
                                }}>
                                    {review.category}
                                </span>
                            </div>
                            <div style={{
                                width: '24px',
                                height: '24px',
                                backgroundColor: '#666',
                                borderRadius: '4px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                cursor: 'pointer'
                            }}>
                                <span style={{ color: 'white', fontSize: '12px' }}>💬</span>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default Participant