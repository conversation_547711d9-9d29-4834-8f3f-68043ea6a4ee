import React from 'react';
import { Link } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

const JobSupportReviews = () => {
  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link to="/" className="flex items-center text-teal-600 hover:text-teal-700 transition-colors">
            <FaArrowLeft className="mr-2" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-teal-600 mb-6">
            Job Support Reviews
          </h1>



          {/* Navigation Menu */}
          <div className="bg-teal-600 rounded-lg p-1 mb-8">
            <div className="flex flex-wrap gap-1 text-sm">
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">About Henry Harvin</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Henry Harvin in Media</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our Affiliations</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our Customers</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our CSR Activities</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our Gallery</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Participant Reviews</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Corporate Training Reviews</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">College Training Reviews</button>
              <button className="px-4 py-2 bg-white text-teal-600 rounded font-medium">Job Support Reviews</button>
            </div>
          </div>
        </div>

        {/* Job Support Email Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Gmail Card 1 - Parvathy G */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200">
            {/* Gmail Header */}
            <div className="bg-gray-50 p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">M</span>
                  </div>
                  <span className="text-sm font-medium text-gray-700">Gmail</span>
                </div>
                <span className="text-xs text-gray-500">5/14/25, 3:06 PM</span>
              </div>
              <div className="text-sm text-gray-600">
                <div className="font-medium">Parvathy G &lt;<EMAIL>&gt;</div>
              </div>
            </div>

            {/* Email Content */}
            <div className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-3">
                Congratulations! Welcome to – Let's Begin Your Onboarding
              </h3>

              <div className="space-y-3 text-sm text-gray-700">
                <div>
                  <span className="font-medium">From:</span> People & Culture HCI Group &lt;<EMAIL>&gt;
                </div>
                <div>
                  <span className="font-medium">To:</span> PARVATHY G
                </div>
                <div>
                  <span className="font-medium">Subject:</span> Raj K Pillai, Harjinderpal K
                </div>
              </div>

              {/* HCI Australia Logo */}
              <div className="flex items-center justify-center my-4">
                <div className="bg-teal-600 rounded-lg p-3">
                  <span className="text-white font-bold text-lg">HCI Australia</span>
                </div>
              </div>

              <div className="text-sm text-gray-600 mb-4">
                <p className="mb-2">Dear PARVATHY,</p>
                <p>We are delighted to confirm the acceptance of your employment agreement and your decision to join as Instructional Designer. HRMS...</p>
              </div>

              {/* Job Support Badge */}
              <div className="flex justify-center">
                <span className="bg-teal-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  Job Placement Success
                </span>
              </div>
            </div>
          </div>

          {/* Gmail Card 2 - Second Email */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200">
            {/* Gmail Header */}
            <div className="bg-gray-50 p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">M</span>
                  </div>
                  <span className="text-sm font-medium text-gray-700">Gmail</span>
                </div>
                <span className="text-xs text-gray-500">5/14/25, 3:06 PM</span>
              </div>
              <div className="text-sm text-gray-600">
                <div className="font-medium">Parvathy G &lt;<EMAIL>&gt;</div>
              </div>
            </div>

            {/* Email Content */}
            <div className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-3">
                Congratulations! Welcome to – Let's Begin Your Onboarding
              </h3>

              <div className="space-y-3 text-sm text-gray-700">
                <div>
                  <span className="font-medium">From:</span> People & Culture HCI Group &lt;<EMAIL>&gt;
                </div>
                <div>
                  <span className="font-medium">To:</span> PARVATHY G
                </div>
                <div>
                  <span className="font-medium">Subject:</span> Raj K Pillai, Harjinderpal K
                </div>
              </div>

              {/* HCI Australia Logo */}
              <div className="flex items-center justify-center my-4">
                <div className="bg-teal-600 rounded-lg p-3">
                  <span className="text-white font-bold text-lg">HCI Australia</span>
                </div>
              </div>

              <div className="text-sm text-gray-600 mb-4">
                <p className="mb-2">Dear PARVATHY,</p>
                <p>You have also been successfully registered as an onboarding user in our HRMS platform, MyHCI (ELMO). As part of the onboarding documentation process...</p>
              </div>

              {/* Job Support Badge */}
              <div className="flex justify-center">
                <span className="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  HRMS Registration
                </span>
              </div>
            </div>
          </div>

          {/* Chat Support Card */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200">
            {/* Chat Header */}
            <div className="bg-gray-50 p-4 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">💬</span>
                </div>
                <span className="text-sm font-medium text-gray-700">Live Chat Support</span>
              </div>
            </div>

            {/* Chat Content */}
            <div className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-3">
                Hi! I am Sushmita
              </h3>

              <div className="bg-gray-100 rounded-lg p-4 mb-4">
                <p className="text-sm text-gray-700 mb-2">
                  "May I know which course you are looking for?"
                </p>
                <p className="text-sm text-gray-700">
                  "I will help you with more information."
                </p>
              </div>

              <div className="text-sm text-gray-600 mb-4">
                <p>Our dedicated support team is available 24/7 to assist with:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Course guidance</li>
                  <li>Job placement support</li>
                  <li>Career counseling</li>
                </ul>
              </div>

              {/* Support Badge */}
              <div className="flex justify-center">
                <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  24/7 Support Available
                </span>
              </div>
            </div>
          </div>

          {/* Additional Job Support Cards */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200">
            <div className="bg-gradient-to-r from-teal-500 to-teal-600 p-6 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-white text-xl font-bold">Career Guidance</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-700 text-sm mb-4">
                Personalized career counseling and guidance to help you choose the right path for your professional growth.
              </p>
              <div className="flex justify-center">
                <span className="bg-teal-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  Expert Guidance
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
                <span className="text-2xl">📝</span>
              </div>
              <h3 className="text-white text-xl font-bold">Resume Building</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-700 text-sm mb-4">
                Professional resume writing and optimization services to make you stand out to potential employers.
              </p>
              <div className="flex justify-center">
                <span className="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  Professional Resume
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="text-white text-xl font-bold">Interview Prep</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-700 text-sm mb-4">
                Mock interviews and preparation sessions to boost your confidence and improve your interview skills.
              </p>
              <div className="flex justify-center">
                <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  Interview Ready
                </span>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default JobSupportReviews;