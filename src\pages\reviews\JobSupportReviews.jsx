import React from 'react';
import { Link } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

const JobSupportReviews = () => {
  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link to="/" className="flex items-center text-teal-600 hover:text-teal-700 transition-colors">
            <FaArrowLeft className="mr-2" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-teal-600 mb-6">
            Job Support Reviews
          </h1>



          {/* Navigation Menu */}
          <div className="bg-teal-600 rounded-lg p-1 mb-8">
            <div className="flex flex-wrap gap-1 text-sm">
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">About Henry Harvin</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Henry Harvin in Media</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our Affiliations</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our Customers</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our CSR Activities</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Our Gallery</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Participant Reviews</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">Corporate Training Reviews</button>
              <button className="px-4 py-2 text-white hover:bg-teal-700 rounded transition-colors">College Training Reviews</button>
              <button className="px-4 py-2 bg-white text-teal-600 rounded font-medium">Job Support Reviews</button>
            </div>
          </div>
        </div>

        {/* Content Area - Ready for job support content */}
        <div className="text-center py-16">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Job Support Reviews Coming Soon</h2>
            <p className="text-gray-600 mb-8">
              Explore testimonials from students who have benefited from our job support services.
              Learn how our career guidance, interview preparation, and job placement assistance
              have helped learners secure positions in their desired fields.
            </p>
            <div className="bg-white rounded-lg shadow-md p-8 border border-gray-200">
              <div className="text-6xl mb-4">💼</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Job Support Services</h3>
              <p className="text-gray-600">
                Our comprehensive job support program includes resume building, interview coaching,
                and direct placement assistance to help you land your dream job.
              </p>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default JobSupportReviews;