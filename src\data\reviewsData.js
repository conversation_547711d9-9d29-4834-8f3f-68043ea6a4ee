// Shared reviews data for all review pages
export const reviewsData = [
  {
    id: 1,
    name: "<PERSON>",
    rating: "4.9/5",
    course: "Medical Coding Certification Course Review",
    review: "I am a nurse and was looking for a good medical coding course online as I was planning to move into a different career. Yet, working in shifts, I was afraid to take up a course in medical coding fearing I cannot cope with the timings. But <PERSON>'s Medical coding course came as a boon [...]",
    avatar: "👤",
    category: "Medical Coding Course"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    rating: "4.8/5",
    course: "Medical Coding Course Reviews",
    review: "The online classes provided by <PERSON> are interactive which was quite a lot of things. First, it helped me to understand all the basic concepts, thanks to the experienced trainers in the field who made the concepts easy to learn. Second, I can ask and get cleared of all of my doubts then [...]",
    avatar: "👤",
    category: "Medical Coding Course"
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON>arg",
    rating: "4.7/5",
    course: "<PERSON>rvin Medical Coding Course Review",
    review: "I have always wanted to do a Medical coding course but I was worried about the timing and whether the course will be flexible for me and whether I can cope easily with the course. However, after checking <PERSON>'s medical coding course online I was confident and decided it is the best for me [...]",
    avatar: "👤",
    category: "Medical Coding Course"
  },
  {
    id: 4,
    name: "Simran Sha",
    rating: "4.6/5",
    course: "Henry Harvin Medical Coding Course Training Review",
    review: "Henry Harvin medical coding course helped me greatly to learn and master all the concepts such as ICD-10, ICD-9, and CPT. By the end of the demo class, I decided that this can be the right course for me. My trainer was very helpful and supportive. They can be the best in the [...]",
    avatar: "👤",
    category: "Medical Coding Course"
  },
  {
    id: 5,
    name: "Rahul Dutt",
    rating: "4.9/5",
    course: "Henry Harvin Medical Coding Course Review",
    review: "Opting for this online Medical coding course from Henry Harvin is the best decision I have ever made. One of my friends recommended this course to me and I was skeptical at first. But with the demo class, I decided that this can be the right course for me. My trainer [...]",
    avatar: "👤",
    category: "Medical Coding Course"
  },
  {
    id: 6,
    name: "Shilpa",
    rating: "4.8/5",
    course: "Henry Harvin ACCA Course Review",
    review: "ACCA is globally recognised and one of the most sought after certification by employers worldwide. I study ACCA at Henry Harvin Institute. My experience with Henry Harvin is extremely positive. They are the best in the business. I would recommend Henry Harvin to [...]",
    avatar: "👤",
    category: "Medical Coding Course"
  },
  {
    id: 7,
    name: "Emma Wilson",
    rating: "4.7/5",
    course: "Digital Marketing Course Review",
    review: "The digital marketing course at Henry Harvin was comprehensive and practical. I learned everything from SEO to social media marketing. The hands-on projects helped me understand real-world applications. The instructors were knowledgeable and always available for doubts [...]",
    avatar: "👤",
    category: "Digital Marketing Course"
  },
  {
    id: 8,
    name: "Michael Chen",
    rating: "4.6/5",
    course: "Data Science Certification Review",
    review: "Excellent course structure with practical assignments. The Python and machine learning modules were particularly well-designed. I was able to transition from a non-technical background to landing a data analyst role. The career support was outstanding [...]",
    avatar: "👤",
    category: "Data Science Course"
  },
  {
    id: 9,
    name: "Sarah Johnson",
    rating: "4.8/5",
    course: "Project Management Course Review",
    review: "The PMP certification course was exactly what I needed to advance my career. The instructors were industry experts and the course material was up-to-date with the latest PMBOK guidelines. I passed the exam on my first attempt [...]",
    avatar: "👤",
    category: "Project Management Course"
  },
  {
    id: 10,
    name: "David Kumar",
    rating: "4.9/5",
    course: "Web Development Bootcamp Review",
    review: "From zero to full-stack developer in 6 months! The curriculum was intensive but well-structured. The mentorship and career guidance helped me land my first developer job. Highly recommend for career changers [...]",
    avatar: "👤",
    category: "Web Development Course"
  },
  {
    id: 11,
    name: "Priya Sharma",
    rating: "4.7/5",
    course: "Graphic Design Course Review",
    review: "Creative and inspiring course! Learned Adobe Creative Suite from scratch and built an impressive portfolio. The design principles and practical projects were excellent. Now working as a freelance designer [...]",
    avatar: "👤",
    category: "Graphic Design Course"
  },
  {
    id: 12,
    name: "James Rodriguez",
    rating: "4.8/5",
    course: "Cybersecurity Certification Review",
    review: "Comprehensive cybersecurity training with hands-on labs. The ethical hacking modules were particularly engaging. Gained industry-relevant skills and certifications. Great preparation for cybersecurity roles [...]",
    avatar: "👤",
    category: "Cybersecurity Course"
  }
];

// Function to get paginated reviews
export const getPaginatedReviews = (page = 1, itemsPerPage = 6) => {
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = reviewsData.slice(startIndex, endIndex);
  
  return {
    reviews: paginatedData,
    totalPages: Math.ceil(reviewsData.length / itemsPerPage),
    currentPage: page,
    totalReviews: reviewsData.length,
    hasNextPage: endIndex < reviewsData.length,
    hasPrevPage: page > 1
  };
};
