import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaArrowLeft, FaSearch } from 'react-icons/fa';

const VideoReviews = () => {
  const [selectedCourse, setSelectedCourse] = useState('');

  const videoReviews = [
    {
      id: 1,
      title: "Diploma In Operation Theatre Technician Course Review By Ekta | Brain Bridge DOTT Course Review",
      videoId: "dQw4w9WgXcQ", // YouTube video ID
      thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg"
    },
    {
      id: 2,
      title: "Diploma In Medical Laboratory Technician Course Review By Kirti | Brain Bridge DMLT Course Review",
      videoId: "dQw4w9WgXcQ",
      thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg"
    },
    {
      id: 3,
      title: "Diploma In Operation Theatre Technician Course Review By <PERSON><PERSON> | Brain Bridge DOTT Course Review",
      videoId: "dQw4w9WgXcQ",
      thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg"
    }
  ];

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link to="/" className="flex items-center text-teal-600 hover:text-teal-700 transition-colors">
            <FaArrowLeft className="mr-2" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-teal-600 mb-6">
            Brain Bridge Video Reviews
          </h1>

          {/* Description */}
          <div className="bg-white rounded-lg p-6 mb-6 shadow-sm">
            <p className="text-gray-700 leading-relaxed mb-4">
              Here we are presenting exclusive Brain Bridge Reviews which are given by our students who got benefited from our training. These Video Reviews of Brain Bridge is enough to understand and join with No.1 Career oriented training institute in Delhi NCR. As a small initiative we are uploaded first set of videos from our library. By seeing each and every person who gave review in these videos anyone can understand our motive towards education. These Brain Bridge Reviews itself speaks about how we are different from other training institutes who are just brainwashing the students mind for money.
            </p>
          </div>

          {/* For those who gave reviews section */}
          <div className="bg-white rounded-lg p-6 mb-8 shadow-sm">
            <h2 className="text-xl font-semibold text-teal-600 mb-4">
              For those who gave reviews
            </h2>
            <p className="text-gray-700 mb-4">
              Thanks for the opportunity to let us give better education at the right time in right way. We will support our students all the time if they need us. We will be available at +91 9899577620
            </p>

            {/* Search Section */}
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="flex-1 max-w-md">
                <select
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="">Select Course</option>
                  <option value="medical-coding">Medical Coding</option>
                  <option value="operation-theatre">Operation Theatre Technician</option>
                  <option value="medical-laboratory">Medical Laboratory Technician</option>
                  <option value="data-science">Data Science</option>
                  <option value="digital-marketing">Digital Marketing</option>
                </select>
              </div>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md transition-colors flex items-center gap-2">
                <FaSearch className="text-sm" />
                SEARCH
              </button>

              {/* Phone number button */}
              <div className="bg-green-500 text-white px-4 py-2 rounded-md font-semibold">
                +91 ************
              </div>
            </div>
          </div>
        </div>

        {/* Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {videoReviews.map((video) => (
            <div key={video.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300">
              {/* Video Thumbnail */}
              <div className="relative aspect-video bg-gray-200">
                <iframe
                  src={`https://www.youtube.com/embed/${video.videoId}`}
                  title={video.title}
                  className="w-full h-full"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>

              {/* Video Title */}
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 leading-tight">
                  {video.title}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Chat and WhatsApp buttons */}

      </div>
    </div>
  );
};

export default VideoReviews;