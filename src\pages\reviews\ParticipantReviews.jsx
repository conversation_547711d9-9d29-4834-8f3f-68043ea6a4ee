import React from 'react';
import { Link } from 'react-router-dom';
import { FaArrowLeft, FaComment } from 'react-icons/fa';

const ParticipantReviews = () => {
  const reviews = [
    {
      id: 1,
      name: "<PERSON>",
      rating: "4.9/5",
      course: "Medical Coding Certification Course Review",
      review: "I am a nurse and was looking for a good medical coding course online as I was planning to move into a different career. Yet, working in shifts, I was afraid to take up a course in medical coding fearing I cannot cope with the timings. But <PERSON>'s Medical coding course came as a boon [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      rating: "4.8/5",
      course: "Medical Coding Course Reviews",
      review: "The online classes provided by <PERSON> are interactive which was quite a lot of things. First, it helped me to understand all the basic concepts, thanks to the experienced trainers in the field who made the concepts easy to learn. Second, I can ask and get cleared of all of my doubts then [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      rating: "4.7/5",
      course: "<PERSON>rvin Medical Coding Course Review",
      review: "I have always wanted to do a Medical coding course but I was worried about the timing and whether the course will be flexible for me and whether I can cope easily with the course. However, after checking <PERSON> Harvin's medical coding course online I was confident and decided it is the best for me [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 4,
      name: "Simran Sha",
      rating: "4.6/5",
      course: "Henry Harvin Medical Coding Course Training Review",
      review: "Henry Harvin medical coding course helped me greatly to learn and master all the concepts such as ICD-10, ICD-9, and CPT. By the end of the demo class, I decided that this can be the right course for me. My trainer was very helpful and supportive. They can be the best in the [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 5,
      name: "Rahul Dutt",
      rating: "4.9/5",
      course: "Henry Harvin Medical Coding Course Review",
      review: "Opting for this online Medical coding course from Henry Harvin is the best decision I have ever made. One of my friends recommended this course to me and I was skeptical at first. But with the demo class, I decided that this can be the right course for me. My trainer [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 6,
      name: "Shilpa",
      rating: "4.8/5",
      course: "Henry Harvin ACCA Course Review",
      review: "ACCA is globally recognised and one of the most sought after certification by employers worldwide. I study ACCA at Henry Harvin Institute. My experience with Henry Harvin is extremely positive. They are the best in the business. I would recommend Henry Harvin to [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 7,
      name: "Shilpa",
      rating: "4.8/5",
      course: "Henry Harvin ACCA Course Review",
      review: "ACCA is globally recognised and one of the most sought after certification by employers worldwide. I study ACCA at Henry Harvin Institute. My experience with Henry Harvin is extremely positive. They are the best in the business. I would recommend Henry Harvin to [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 8,
      name: "Shilpa",
      rating: "4.8/5",
      course: "Henry Harvin ACCA Course Review",
      review: "ACCA is globally recognised and one of the most sought after certification by employers worldwide. I study ACCA at Henry Harvin Institute. My experience with Henry Harvin is extremely positive. They are the best in the business. I would recommend Henry Harvin to [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },
    {
      id: 9,
      name: "Shilpa",
      rating: "4.8/5",
      course: "Henry Harvin ACCA Course Review",
      review: "ACCA is globally recognised and one of the most sought after certification by employers worldwide. I study ACCA at Henry Harvin Institute. My experience with Henry Harvin is extremely positive. They are the best in the business. I would recommend Henry Harvin to [...]",
      avatar: "👤",
      category: "Medical Coding Course"
    },



  ];

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link to="/" className="flex items-center text-teal-600 hover:text-teal-700 transition-colors">
            <FaArrowLeft className="mr-2" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Participant Reviews
          </h1>
          <p className="text-lg text-gray-600">
            Read authentic reviews from students who have completed our courses. Discover how our training programs have helped them advance their careers and achieve their learning goals.
          </p>
        </div>

        {/* Reviews Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reviews.map((review) => (
            <div key={review.id} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300">
              <div className="p-6">
                {/* Header with name and rating */}
                <div className="mb-4">
                  <h3 className="text-teal-600 text-base font-bold mb-1">
                    {review.name}, {review.rating} {review.course.includes('Henry Harvin') ? review.course.split('Henry Harvin')[1] : review.course}
                  </h3>
                </div>

                {/* Course title */}
                <h4 className="text-teal-600 text-sm font-bold mb-4 leading-snug">
                  {review.course}
                </h4>

                {/* Review text */}
                <p className="text-gray-700 text-xs leading-relaxed mb-5 text-justify">
                  {review.review}
                </p>

                {/* Footer with avatar and category */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-base">
                      {review.avatar}
                    </div>
                    <span className="text-teal-600 text-xs font-medium">
                      {review.category}
                    </span>
                  </div>
                  <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors">
                    <FaComment className="text-white text-xs" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ParticipantReviews;