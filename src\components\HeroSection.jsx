import React from 'react'
import { FaSearch } from 'react-icons/fa'

const HeroSection = () => {
  const skillTags = [
    'Artificial Intelligence',
    'Web Development',
    'Data Science',
    'Machine Learning',
    'Cybersecurity',
    'Financial Management',
    'Cloud Computing',
    'DevOps'
  ];

  const stats = [
    { number: '460,000+', label: 'Trained' },
    { number: '6,700+', label: 'Reviews with 4.5/5 rating' },
    { number: '7,000+', label: 'Live Classes Every Month' },
    { number: '900+', label: 'Corporate Partners' },
    { number: '210+', label: 'College Partners' },
    { number: '87%', label: 'Reported Career Benefits' }
  ];

  return (
    <section className="bg-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">

          {/* Left Content */}
          <div className="space-y-6">
            {/* Main Heading */}
            <div className="space-y-3">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Master tomorrow's skills today.
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed">
                Excel with India's top upskilling platform.
              </p>
            </div>

            {/* Search Bar */}
            <div className="bg-white border border-gray-300 rounded-lg p-1 shadow-sm max-w-lg">
              <div className="flex items-center">
                <input
                  type="text"
                  placeholder="Tell us what you're looking to learn"
                  className="flex-1 px-4 py-2.5 text-gray-700 placeholder-gray-500 bg-transparent focus:outline-none text-sm"
                />
                <button className="bg-teal-600 text-white p-2.5 rounded-md hover:bg-teal-700 transition-colors duration-300">
                  <FaSearch className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Goal Selection */}
            <div className="space-y-2">
              <p className="text-gray-700 font-medium text-sm">Or select your goal 🎯</p>
              <div className="flex flex-wrap gap-2">
                {skillTags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2.5 py-1.5 bg-gray-100 text-gray-700 rounded text-xs hover:bg-teal-50 hover:text-teal-700 cursor-pointer transition-colors duration-300 border border-gray-200"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content - Professional Image */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative">
              {/* Dotted border frame */}
              <div className="absolute inset-0 border-2 border-dashed border-gray-300 rounded-lg transform translate-x-3 translate-y-3"></div>

              {/* Main content container */}
              <div className="relative bg-white rounded-lg p-6 shadow-lg">
                <div className="text-center">
                  <h2 className="text-lg font-bold text-teal-600 mb-3">BRAIN BRIDGE</h2>

                  {/* Professional person image */}
                  <div className="mb-3">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=400&fit=crop&crop=face"
                      alt="Professional instructor"
                      className="w-36 h-48 object-cover rounded-lg mx-auto"
                    />
                  </div>

                  {/* Quote */}
                  <p className="text-xs text-gray-600 italic">
                    "The flexibility of online learning helped me finish my degree"
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Section */}
        <div className="mt-12">
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-xl md:text-2xl font-bold text-gray-900 mb-1">
                    {stat.number}
                  </div>
                  <div className="text-xs text-gray-600 leading-tight">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div className="mt-10 text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-6">
            7000+ Reviews with 4.6/5 Avg Rating
          </h3>

          {/* Review cards placeholder */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div key={item} className="bg-gray-100 rounded-lg h-24 flex items-center justify-center">
                <div className="text-gray-400 text-sm">Review Card {item}</div>
              </div>
            ))}
          </div>

          {/* Our Affiliations Button */}
          <button className="bg-teal-600 text-white px-5 py-2.5 rounded-lg hover:bg-teal-700 transition-colors duration-300 text-sm">
            Our Affiliations →
          </button>
        </div>

        
      </div>
    </section>
  )
}

export default HeroSection
